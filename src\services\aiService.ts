// AI服务 - 处理与AI提供商的通信

import type { Message, AIProvider, ChatSettings } from '@/types'

export interface StreamingResponse {
  id: string
  content: string
  isComplete: boolean
  error?: string
  model?: string
  tokens?: number
  // 思考模型支持
  reasoning?: string
  reasoningComplete?: boolean
}

export interface ChatCompletionRequest {
  messages: Array<{
    role: 'user' | 'assistant' | 'system'
    content: string
  }>
  model: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stream?: boolean
}

export class AIService {
  private provider: AIProvider
  private settings: ChatSettings
  private modelId?: string
  private abortController: AbortController | null = null

  constructor(provider: AIProvider, settings: ChatSettings, modelId?: string) {
    this.provider = provider
    this.settings = settings
    this.modelId = modelId
  }

  // 发送聊天请求（流式）
  async *sendChatStream(
    messages: Message[],
    onProgress?: (chunk: StreamingResponse) => void
  ): AsyncGenerator<StreamingResponse, void, unknown> {
    this.abortController = new AbortController()

    // 检查是否为Google Gemini API
    const isGoogleAPI = this.provider.id === 'google'

    // 检查是否使用OpenAI兼容接口
    const isGoogleOpenAICompatible = isGoogleAPI && this.provider.baseUrl?.includes('/openai')

    if (isGoogleAPI && !isGoogleOpenAICompatible) {
      // 使用原生Google Gemini API
      yield* this.sendGoogleChatStream(messages, onProgress)
      return
    }



    // 构建消息列表，包含系统提示词
    const requestMessages: any[] = []

    // 如果有系统提示词，添加到消息开头
    if (this.settings.systemPrompt && this.settings.systemPrompt.trim()) {
      requestMessages.push({
        role: 'system',
        content: this.settings.systemPrompt.trim()
      })
      // 系统提示词已添加
    }

    // 添加对话消息
    requestMessages.push(...messages.map(msg => ({
      role: msg.role,
      content: msg.content
    })))

    const requestBody: ChatCompletionRequest = {
      messages: requestMessages,
      model: this.getSelectedModel(),
      temperature: this.settings.temperature,
      maxTokens: this.settings.maxTokens,
      topP: this.settings.topP,
      frequencyPenalty: this.settings.frequencyPenalty,
      presencePenalty: this.settings.presencePenalty,
      stream: true
    }

    try {
      // 检查是否为Google OpenAI兼容接口
      const isGoogleOpenAICompatible = this.provider.id === 'google' && this.provider.baseUrl?.includes('/openai')

      let headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Desktop-Client/1.0.0'
      }

      // 根据API类型设置认证头
      if (isGoogleOpenAICompatible) {
        // Google OpenAI兼容接口使用x-goog-api-key
        headers['x-goog-api-key'] = this.provider.apiKey || ''
        console.log('🔍 使用Google OpenAI兼容接口')
      } else {
        // 标准OpenAI接口使用Authorization Bearer
        headers['Authorization'] = `Bearer ${this.provider.apiKey}`
        console.log('🔍 使用标准OpenAI接口')
      }

      const url = `${this.provider.baseUrl}/chat/completions`
      console.log('📡 发送请求到:', url)
      console.log('📋 请求头:', headers)
      console.log('📄 请求体:', JSON.stringify(requestBody, null, 2))

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: this.abortController.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      if (!response.body) {
        throw new Error('响应体为空')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      let messageId = crypto.randomUUID()
      let fullContent = ''
      let fullReasoning = ''
      let reasoningComplete = false
      let lastModel = ''
      let lastTokens = 0

      try {
        while (true) {
          const { done, value } = await reader.read()
          
          if (done) {
            // 发送完成信号
            const finalResponse: StreamingResponse = {
              id: messageId,
              content: '',
              isComplete: true,
              model: lastModel,
              tokens: lastTokens,
              reasoning: fullReasoning || undefined,
              reasoningComplete: fullReasoning ? true : undefined
            }

            // 流式响应完成
            
            if (onProgress) onProgress(finalResponse)
            yield finalResponse
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            const trimmedLine = line.trim()
            
            if (trimmedLine === '') continue
            if (trimmedLine === 'data: [DONE]') continue
            if (!trimmedLine.startsWith('data: ')) continue

            try {
              const jsonStr = trimmedLine.slice(6) // 移除 'data: ' 前缀
              const data = JSON.parse(jsonStr)

              // API响应处理

              if (data.choices && data.choices[0]) {
                const choice = data.choices[0]
                const delta = choice.delta

                // 更新模型和token信息
                if (data.model) lastModel = data.model
                if (data.usage?.total_tokens) lastTokens = data.usage.total_tokens

                // 处理思考过程 (DeepSeek使用reasoning_content字段)
                if (delta.reasoning_content) {
                  fullReasoning += delta.reasoning_content

                  const reasoningResponse: StreamingResponse = {
                    id: messageId,
                    content: '',
                    reasoning: fullReasoning,
                    reasoningComplete: false,
                    isComplete: false,
                    model: data.model
                  }

                  if (onProgress) onProgress(reasoningResponse)
                  yield reasoningResponse
                  // 思考过程处理
                }

                // 处理主要内容
                if (delta.content) {
                  // 如果这是第一次收到content且之前有reasoning，标记思考完成
                  if (fullContent === '' && fullReasoning && !reasoningComplete) {
                    reasoningComplete = true
                    const reasoningCompleteResponse: StreamingResponse = {
                      id: messageId,
                      content: '',
                      reasoning: fullReasoning,
                      reasoningComplete: true,
                      isComplete: false,
                      model: data.model
                    }

                    if (onProgress) onProgress(reasoningCompleteResponse)
                    yield reasoningCompleteResponse
                    // 思考过程完成
                  }

                  fullContent += delta.content

                  const streamResponse: StreamingResponse = {
                    id: messageId,
                    content: delta.content,
                    isComplete: false,
                    model: data.model,
                    tokens: data.usage?.total_tokens
                  }

                  if (onProgress) onProgress(streamResponse)
                  yield streamResponse
                  // 答案内容处理
                }
              }
            } catch (parseError) {
              console.warn('解析SSE数据失败:', parseError, trimmedLine)
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return
        }
        
        const errorResponse: StreamingResponse = {
          id: crypto.randomUUID(),
          content: '',
          isComplete: true,
          error: error.message
        }
        
        if (onProgress) onProgress(errorResponse)
        yield errorResponse
      }
    }
  }

  // Google Gemini API 流式处理
  private async *sendGoogleChatStream(
    messages: Message[],
    onProgress?: (chunk: StreamingResponse) => void
  ): AsyncGenerator<StreamingResponse, void, unknown> {

    console.log('🎯 开始Google API调用')
    console.log('📋 消息数量:', messages.length)
    console.log('🔑 API密钥状态:', this.provider.apiKey ? '已配置' : '未配置')

    // 构建Google Gemini API格式的请求
    const contents: any[] = []
    
    // 处理系统提示词和对话消息
    let systemInstruction = this.settings.systemPrompt?.trim() || '你是一个有用的AI助手，请用中文回答问题。'
    
    // 转换消息格式
    for (const msg of messages) {
      if (msg.role === 'system') {
        systemInstruction = msg.content
        continue
      }
      
      contents.push({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      })
    }

    const requestBody = {
      contents,
      systemInstruction: {
        parts: [{ text: systemInstruction }]
      },
      generationConfig: {
        temperature: this.settings.temperature,
        maxOutputTokens: this.settings.maxTokens,
        topP: this.settings.topP,
        candidateCount: 1
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH", 
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    }

    const modelName = this.getSelectedModel()

    if (!this.provider.apiKey || !this.provider.apiKey.trim()) {
      throw new Error('Google API密钥未配置，请在设置中配置Google API密钥')
    }

    const url = `${this.provider.baseUrl}/models/${modelName}:streamGenerateContent?key=${this.provider.apiKey}`

    console.log('🌐 Google流式请求URL:', url)
    console.log('🤖 使用模型:', modelName)
    console.log('📤 Google流式请求体:', JSON.stringify(requestBody, null, 2))

    // 移除调试弹窗

    // 发送Google Gemini请求

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Desktop-Client/1.0.0'
        },
        body: JSON.stringify(requestBody),
        signal: this.abortController.signal
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Google API错误响应:', errorText)
        throw new Error(`Google API错误 ${response.status}: ${response.statusText}\n详细信息: ${errorText}`)
      }

      if (!response.body) {
        throw new Error('响应体为空')
      }

      console.log('📡 开始读取Google API流式响应')
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''
      let messageId = crypto.randomUUID()
      let fullContent = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          
          if (done) {
            // 发送完成信号
            const finalResponse: StreamingResponse = {
              id: messageId,
              content: '',
              isComplete: true,
              model: modelName
            }

            // Google流式响应完成
            
            if (onProgress) onProgress(finalResponse)
            yield finalResponse
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            const trimmedLine = line.trim()

            if (trimmedLine === '') continue

            try {
              // Google API流式响应可能是多种格式
              let data

              // 尝试解析为SSE格式 (data: {...})
              if (trimmedLine.startsWith('data: ')) {
                const jsonStr = trimmedLine.slice(6)
                if (jsonStr === '[DONE]') continue
                data = JSON.parse(jsonStr)
              } else {
                // 尝试解析为纯JSON格式
                data = JSON.parse(trimmedLine)
              }

              console.log('Google API响应数据:', data)

              // 处理错误响应
              if (data.error) {
                throw new Error(`Google API错误: ${data.error.message || JSON.stringify(data.error)}`)
              }

              // Google API响应处理
              if (data.candidates && data.candidates[0]) {
                const candidate = data.candidates[0]

                // 检查安全过滤
                if (candidate.finishReason === 'SAFETY') {
                  throw new Error('内容被Google安全过滤器阻止，请尝试修改您的问题')
                }

                if (candidate.content && candidate.content.parts) {
                  for (const part of candidate.content.parts) {
                    if (part.text) {
                      fullContent += part.text

                      const streamResponse: StreamingResponse = {
                        id: messageId,
                        content: part.text,
                        isComplete: false,
                        model: modelName
                      }

                      console.log('Google流式输出:', part.text.substring(0, 100))
                      if (onProgress) onProgress(streamResponse)
                      yield streamResponse
                    }
                  }
                }

                // 检查是否完成
                if (candidate.finishReason && candidate.finishReason !== 'STOP') {
                  console.warn('Google API非正常结束:', candidate.finishReason)
                }

                if (candidate.finishReason) {
                  break
                }
              }
            } catch (parseError) {
              console.warn('解析Google响应失败:', parseError, '原始数据:', trimmedLine)
              // 如果解析失败，尝试作为纯文本处理
              if (trimmedLine.length > 0 && !trimmedLine.startsWith('{')) {
                const streamResponse: StreamingResponse = {
                  id: messageId,
                  content: trimmedLine,
                  isComplete: false,
                  model: modelName
                }
                if (onProgress) onProgress(streamResponse)
                yield streamResponse
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

    } catch (error) {
      console.error('🔥 Google API调用异常:', error)
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('🛑 Google API请求被取消')
          return
        }

        console.error('❌ Google API错误详情:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        })

        const errorResponse: StreamingResponse = {
          id: crypto.randomUUID(),
          content: '',
          isComplete: true,
          error: `Google API错误: ${error.message}`
        }

        if (onProgress) onProgress(errorResponse)
        yield errorResponse
      }
    }
  }

  // 发送聊天请求（非流式）
  async sendChat(messages: Message[]): Promise<Message> {
    // 检查是否为Google Gemini API
    const isGoogleAPI = this.provider.id === 'google'

    // 检查是否使用OpenAI兼容接口
    const isGoogleOpenAICompatible = isGoogleAPI && this.provider.baseUrl?.includes('/openai')

    if (isGoogleAPI && !isGoogleOpenAICompatible) {
      // 使用原生Google Gemini API
      return this.sendGoogleChat(messages)
    }

    const requestBody: ChatCompletionRequest = {
      messages: messages.map(msg => ({
        id: msg.id,
        conversationId: msg.conversationId,
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      })),
      model: this.getSelectedModel(),
      temperature: this.settings.temperature,
      maxTokens: this.settings.maxTokens,
      topP: this.settings.topP,
      frequencyPenalty: this.settings.frequencyPenalty,
      presencePenalty: this.settings.presencePenalty,
      stream: false
    }

    try {
      // 检查是否为Google OpenAI兼容接口
      const isGoogleOpenAICompatible = this.provider.id === 'google' && this.provider.baseUrl?.includes('/openai')

      let headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'AI-Desktop-Client/1.0.0'
      }

      // 根据API类型设置认证头
      if (isGoogleOpenAICompatible) {
        // Google OpenAI兼容接口使用x-goog-api-key
        headers['x-goog-api-key'] = this.provider.apiKey || ''
      } else {
        // 标准OpenAI接口使用Authorization Bearer
        headers['Authorization'] = `Bearer ${this.provider.apiKey}`
      }

      const response = await fetch(`${this.provider.baseUrl}/chat/completions`, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.choices && data.choices[0]) {
        const choice = data.choices[0]
        
        return {
          id: crypto.randomUUID(),
          conversationId: messages[0]?.conversationId || '',
          role: 'assistant',
          content: choice.message.content,
          timestamp: new Date(),
          model: data.model,
          tokens: data.usage?.total_tokens
        }
      }

      throw new Error('无效的响应格式')
    } catch (error) {
      throw new Error(`AI请求失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // Google Gemini API 非流式处理
  private async sendGoogleChat(messages: Message[]): Promise<Message> {
    // 构建Google Gemini API格式的请求
    const contents: any[] = []
    
    // 处理系统提示词和对话消息
    let systemInstruction = this.settings.systemPrompt?.trim() || '你是一个有用的AI助手，请用中文回答问题。'
    
    // 转换消息格式
    for (const msg of messages) {
      if (msg.role === 'system') {
        systemInstruction = msg.content
        continue
      }
      
      contents.push({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      })
    }

    const requestBody = {
      contents,
      systemInstruction: {
        parts: [{ text: systemInstruction }]
      },
      generationConfig: {
        temperature: this.settings.temperature,
        maxOutputTokens: this.settings.maxTokens,
        topP: this.settings.topP,
        candidateCount: 1
      }
    }

    const modelName = this.getSelectedModel()

    if (!this.provider.apiKey || !this.provider.apiKey.trim()) {
      throw new Error('Google API密钥未配置，请在设置中配置Google API密钥')
    }

    const url = `${this.provider.baseUrl}/models/${modelName}:generateContent?key=${this.provider.apiKey}`

    console.log('Google非流式请求URL:', url)
    console.log('Google非流式请求体:', JSON.stringify(requestBody, null, 2))

    // 发送Google Gemini非流式请求

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Desktop-Client/1.0.0'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Google API非流式错误响应:', errorText)
        throw new Error(`Google API错误 ${response.status}: ${response.statusText}\n详细信息: ${errorText}`)
      }

      const data = await response.json()
      console.log('Google API非流式响应:', data)

      // 处理错误响应
      if (data.error) {
        throw new Error(`Google API错误: ${data.error.message || JSON.stringify(data.error)}`)
      }

      if (data.candidates && data.candidates[0]) {
        const candidate = data.candidates[0]

        // 检查安全过滤
        if (candidate.finishReason === 'SAFETY') {
          throw new Error('内容被Google安全过滤器阻止，请尝试修改您的问题')
        }

        if (candidate.content) {
          let content = ''

          if (candidate.content.parts) {
            content = candidate.content.parts
              .filter((part: any) => part.text)
              .map((part: any) => part.text)
              .join('')
          }

          return {
            id: crypto.randomUUID(),
            conversationId: messages[0]?.conversationId || '',
            role: 'assistant',
            content,
            timestamp: new Date(),
            model: modelName,
            tokens: data.usageMetadata?.totalTokenCount
          }
        }
      }

      throw new Error('无效的Google API响应格式: ' + JSON.stringify(data))
    } catch (error) {
      throw new Error(`Google AI请求失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  // 取消当前请求
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
  }

  // 获取选中的模型
  private getSelectedModel(): string {
    // 如果指定了模型ID，优先使用
    if (this.modelId) {
      const model = this.provider.models.find((m: any) => m.id === this.modelId)
      if (model) {
        // 对于Google API，使用模型ID；对于其他API，使用模型名称
        return this.provider.id === 'google' ? model.id : model.name
      }
    }

    // 否则使用提供商的选中模型
    const selectedModels = this.provider.selectedModels
    if (selectedModels && selectedModels.length > 0) {
      return selectedModels[0]
    }

    // 返回第一个可用模型
    const firstModel = this.provider.models[0]
    if (firstModel) {
      return this.provider.id === 'google' ? firstModel.id : firstModel.name
    }

    return 'gpt-3.5-turbo'
  }

  // 更新提供商配置
  updateProvider(provider: AIProvider): void {
    this.provider = provider
  }

  // 更新聊天设置
  updateSettings(settings: ChatSettings, modelId?: string): void {
    this.settings = settings
    if (modelId !== undefined) {
      this.modelId = modelId
    }
  }

  // 检查提供商是否可用
  async checkAvailability(): Promise<boolean> {
    try {
      // 检查是否为Google API
      const isGoogleAPI = this.provider.id === 'google'
      const isGoogleOpenAICompatible = isGoogleAPI && this.provider.baseUrl?.includes('/openai')

      let headers: Record<string, string> = {
        'User-Agent': 'AI-Desktop-Client/1.0.0'
      }

      let url = `${this.provider.baseUrl}/models`

      // 根据API类型设置认证
      if (isGoogleAPI && !isGoogleOpenAICompatible) {
        // Google原生API使用URL参数认证
        url += `?key=${this.provider.apiKey}`
      } else if (isGoogleOpenAICompatible) {
        // Google OpenAI兼容接口使用x-goog-api-key
        headers['x-goog-api-key'] = this.provider.apiKey || ''
      } else {
        // 标准OpenAI接口使用Authorization Bearer
        headers['Authorization'] = `Bearer ${this.provider.apiKey}`
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
        signal: AbortSignal.timeout(5000) // 5秒超时
      })

      return response.ok
    } catch (error) {
      return false
    }
  }

  // 获取模型列表
  async getModels(): Promise<any[]> {
    try {
      // 检查是否为Google API
      const isGoogleAPI = this.provider.id === 'google'
      const isGoogleOpenAICompatible = isGoogleAPI && this.provider.baseUrl?.includes('/openai')

      let headers: Record<string, string> = {
        'User-Agent': 'AI-Desktop-Client/1.0.0'
      }

      let url = `${this.provider.baseUrl}/models`

      // 根据API类型设置认证
      if (isGoogleAPI && !isGoogleOpenAICompatible) {
        // Google原生API使用URL参数认证
        url += `?key=${this.provider.apiKey}`
      } else if (isGoogleOpenAICompatible) {
        // Google OpenAI兼容接口使用x-goog-api-key
        headers['x-goog-api-key'] = this.provider.apiKey || ''
      } else {
        // 标准OpenAI接口使用Authorization Bearer
        headers['Authorization'] = `Bearer ${this.provider.apiKey}`
      }

      console.log('🔍 获取模型列表，URL:', url)
      console.log('🔍 请求头:', headers)

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('获取模型列表失败:', response.status, errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('✅ 获取模型列表成功:', data)

      // Google原生API返回格式为 {models: [...]}，OpenAI格式为 {data: [...]}
      if (isGoogleAPI && !isGoogleOpenAICompatible) {
        return data.models || []
      } else {
        return data.data || []
      }
    } catch (error) {
      console.error('获取模型列表异常:', error)
      return []
    }
  }
}

// AI服务工厂
export class AIServiceFactory {
  private static services = new Map<string, AIService>()

  static createService(provider: AIProvider, settings: ChatSettings, modelId?: string): AIService {
    const key = `${provider.id}-${provider.name}-${modelId || 'default'}`

    // 检查是否为思考模型
    const model = provider.models.find((m: any) => m.id === modelId)
    const isReasoningModel = model?.isReasoningModel || false

    if (!this.services.has(key)) {
      // 如果有API key，使用真实服务；否则使用模拟服务
      if (provider.apiKey && provider.apiKey.trim()) {
        this.services.set(key, new AIService(provider, settings, modelId))
      } else {
        // 使用模拟AI服务
        this.services.set(key, new MockAIService(50, isReasoningModel) as any)
      }
    } else {
      // 更新现有服务的配置
      const service = this.services.get(key)!
      if (service instanceof AIService) {
        service.updateProvider(provider)
        service.updateSettings(settings, modelId)
      }
    }

    return this.services.get(key)!
  }

  static getService(providerId: string): AIService | null {
    for (const [key, service] of this.services.entries()) {
      if (key.startsWith(providerId)) {
        return service
      }
    }
    return null
  }

  static clearServices(): void {
    // 取消所有进行中的请求
    for (const service of this.services.values()) {
      service.cancelRequest()
    }
    this.services.clear()
  }
}

// 模拟AI服务（用于开发和测试）
export class MockAIService {
  private delay: number
  private isReasoningModel: boolean

  constructor(delay: number = 50, isReasoningModel: boolean = false) {
    this.delay = delay
    this.isReasoningModel = isReasoningModel
  }

  async *sendChatStream(
    messages: Message[],
    onProgress?: (chunk: StreamingResponse) => void
  ): AsyncGenerator<StreamingResponse, void, unknown> {
    const userMessage = messages[messages.length - 1]
    const messageId = crypto.randomUUID()

    if (this.isReasoningModel) {
      // 思考模型：先输出思考过程，再输出答案
      yield* this.generateReasoningResponse(userMessage.content, messageId, onProgress)
    } else {
      // 普通模型：直接输出答案
      yield* this.generateNormalResponse(userMessage.content, messageId, onProgress)
    }
  }

  private async *generateReasoningResponse(
    userInput: string,
    messageId: string,
    onProgress?: (chunk: StreamingResponse) => void
  ): AsyncGenerator<StreamingResponse, void, unknown> {
    // 第一阶段：思考过程
    const reasoningText = this.generateReasoningProcess(userInput)

    // 流式输出思考过程
    for (let i = 0; i < reasoningText.length; i += 5) {
      const chunk = reasoningText.slice(0, i + 5)

      const streamResponse: StreamingResponse = {
        id: messageId,
        content: '',
        reasoning: chunk,
        reasoningComplete: false,
        isComplete: false,
        model: 'deepseek-reasoner'
      }

      if (onProgress) onProgress(streamResponse)
      yield streamResponse

      await new Promise(resolve => setTimeout(resolve, this.delay))
    }

    // 思考完成
    const reasoningCompleteResponse: StreamingResponse = {
      id: messageId,
      content: '',
      reasoning: reasoningText,
      reasoningComplete: true,
      isComplete: false,
      model: 'deepseek-reasoner'
    }

    if (onProgress) onProgress(reasoningCompleteResponse)
    yield reasoningCompleteResponse

    // 第二阶段：输出最终答案
    const responseText = this.generateMockResponse(userInput)

    for (let i = 0; i < responseText.length; i += 3) {
      const chunk = responseText.slice(i, i + 3)

      const streamResponse: StreamingResponse = {
        id: messageId,
        content: chunk,
        isComplete: false,
        model: 'deepseek-reasoner'
      }

      if (onProgress) onProgress(streamResponse)
      yield streamResponse

      await new Promise(resolve => setTimeout(resolve, this.delay))
    }

    // 发送完成信号
    const finalResponse: StreamingResponse = {
      id: messageId,
      content: '',
      isComplete: true,
      model: 'deepseek-reasoner',
      tokens: Math.floor((reasoningText.length + responseText.length) / 4)
    }

    if (onProgress) onProgress(finalResponse)
    yield finalResponse
  }

  private async *generateNormalResponse(
    userInput: string,
    messageId: string,
    onProgress?: (chunk: StreamingResponse) => void
  ): AsyncGenerator<StreamingResponse, void, unknown> {
    const responseText = this.generateMockResponse(userInput)

    // 模拟流式输出
    for (let i = 0; i < responseText.length; i += 3) {
      const chunk = responseText.slice(i, i + 3)

      const streamResponse: StreamingResponse = {
        id: messageId,
        content: chunk,
        isComplete: false,
        model: 'mock-model'
      }

      if (onProgress) onProgress(streamResponse)
      yield streamResponse

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, this.delay))
    }

    // 发送完成信号
    const finalResponse: StreamingResponse = {
      id: messageId,
      content: '',
      isComplete: true,
      model: 'mock-model',
      tokens: Math.floor(responseText.length / 4)
    }

    if (onProgress) onProgress(finalResponse)
    yield finalResponse
  }

  private generateReasoningProcess(userInput: string): string {
    const reasoningTemplates = [
      `让我分析一下这个问题："${userInput}"

首先，我需要理解用户的核心需求：
- 用户想要了解什么？
- 这个问题涉及哪些方面？
- 我应该从什么角度来回答？

接下来，我会考虑可能的解决方案：
1. 直接回答法：给出简洁明了的答案
2. 详细解释法：提供深入的分析和说明
3. 实例说明法：通过具体例子来阐述

综合考虑，我认为最好的方法是结合多种方式，既要给出明确的答案，也要提供必要的解释和例子。`,

      `思考用户的问题："${userInput}"

这个问题让我想到几个关键点：
- 问题的背景和上下文
- 可能的多种理解方式
- 最有价值的回答角度

我需要确保我的回答：
✓ 准确回应用户的真实需求
✓ 提供实用的信息或建议
✓ 语言清晰易懂
✓ 结构化组织内容

基于这些考虑，我将构建一个全面而有用的回答。`
    ]

    return reasoningTemplates[Math.floor(Math.random() * reasoningTemplates.length)]
  }

  private generateMockResponse(userInput: string): string {
    const responses = [
      `这是对"${userInput}"的回复。我是AI助手，很高兴为您服务！\n\n我可以帮助您解决各种问题，包括：\n- 编程和技术问题\n- 写作和创意\n- 学习和研究\n- 日常生活建议\n\n请随时告诉我您需要什么帮助。`,
      
      `感谢您的问题："${userInput}"。\n\n让我为您详细解答：\n\n1. 首先，这是一个很好的问题\n2. 其次，我会尽力提供准确的信息\n3. 最后，如果您需要更多帮助，请随时询问\n\n希望这个回答对您有帮助！`,
      
      `关于"${userInput}"，我想分享一些见解：\n\n这是一个复杂的话题，涉及多个方面。让我逐一为您分析：\n\n**要点一：** 基础概念很重要\n**要点二：** 实践应用需要考虑\n**要点三：** 持续学习是关键\n\n如果您想深入了解某个特定方面，请告诉我！`
    ]

    return responses[Math.floor(Math.random() * responses.length)]
  }
}