<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        #output {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Google API 修复测试</h1>
    
    <div class="container">
        <h3>测试配置</h3>
        <label>API Key:</label>
        <input type="text" id="apiKey" placeholder="输入你的Google API Key">
        
        <label>测试消息:</label>
        <input type="text" id="testMessage" value="你好，请介绍一下你自己">
        
        <button onclick="testGoogleAPI()">测试 Google API</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>
    
    <div class="container">
        <h3>测试结果</h3>
        <div id="output">等待测试...</div>
    </div>

    <script type="module">
        // 模拟修复后的Google API调用逻辑
        async function* sendGoogleChatStream(messages, apiKey) {
            const baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
            const modelName = 'gemini-1.5-flash';
            
            // 构建请求体
            const contents = messages.map(msg => ({
                role: msg.role === 'assistant' ? 'model' : 'user',
                parts: [{ text: msg.content }]
            }));

            const requestBody = {
                contents,
                systemInstruction: {
                    parts: [{ text: '你是一个有用的AI助手，请用中文回答问题。' }]
                },
                generationConfig: {
                    temperature: 0.7,
                    maxOutputTokens: 2048,
                    topP: 1.0,
                    candidateCount: 1
                }
            };

            const url = `${baseUrl}/models/${modelName}:streamGenerateContent?key=${apiKey}`;
            
            console.log('Google流式请求URL:', url);
            console.log('Google流式请求体:', JSON.stringify(requestBody, null, 2));

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'AI-Desktop-Client/1.0.0'
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('Google API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Google API错误响应:', errorText);
                    throw new Error(`Google API错误 ${response.status}: ${response.statusText}\n详细信息: ${errorText}`);
                }

                if (!response.body) {
                    throw new Error('响应体为空');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let messageId = crypto.randomUUID();

                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) {
                            yield {
                                id: messageId,
                                content: '',
                                isComplete: true,
                                model: modelName
                            };
                            break;
                        }

                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');
                        buffer = lines.pop() || '';

                        for (const line of lines) {
                            const trimmedLine = line.trim();
                            if (trimmedLine === '') continue;

                            try {
                                let data;
                                if (trimmedLine.startsWith('data: ')) {
                                    const jsonStr = trimmedLine.slice(6);
                                    if (jsonStr === '[DONE]') continue;
                                    data = JSON.parse(jsonStr);
                                } else {
                                    data = JSON.parse(trimmedLine);
                                }

                                if (data.error) {
                                    throw new Error(`Google API错误: ${data.error.message || JSON.stringify(data.error)}`);
                                }

                                if (data.candidates && data.candidates[0]) {
                                    const candidate = data.candidates[0];

                                    if (candidate.finishReason === 'SAFETY') {
                                        throw new Error('内容被Google安全过滤器阻止，请尝试修改您的问题');
                                    }

                                    if (candidate.content && candidate.content.parts) {
                                        for (const part of candidate.content.parts) {
                                            if (part.text) {
                                                yield {
                                                    id: messageId,
                                                    content: part.text,
                                                    isComplete: false,
                                                    model: modelName
                                                };
                                            }
                                        }
                                    }

                                    if (candidate.finishReason) {
                                        break;
                                    }
                                }
                            } catch (parseError) {
                                console.warn('解析Google响应失败:', parseError, '原始数据:', trimmedLine);
                            }
                        }
                    }
                } finally {
                    reader.releaseLock();
                }

            } catch (error) {
                console.error('Google API调用异常:', error);
                throw error;
            }
        }

        // 测试函数
        window.testGoogleAPI = async function() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const testMessage = document.getElementById('testMessage').value.trim();
            const output = document.getElementById('output');
            
            if (!apiKey) {
                output.textContent = '请输入API Key';
                return;
            }
            
            if (!testMessage) {
                output.textContent = '请输入测试消息';
                return;
            }
            
            output.textContent = '正在测试Google API...\n';
            
            try {
                const messages = [
                    { role: 'user', content: testMessage }
                ];
                
                let fullResponse = '';
                
                for await (const chunk of sendGoogleChatStream(messages, apiKey)) {
                    if (chunk.content) {
                        fullResponse += chunk.content;
                        output.textContent = `测试成功！\n\n收到响应:\n${fullResponse}`;
                    }
                    
                    if (chunk.isComplete) {
                        output.textContent += '\n\n✅ 流式响应完成';
                        break;
                    }
                }
                
            } catch (error) {
                output.textContent = `❌ 测试失败:\n${error.message}`;
                console.error('测试失败:', error);
            }
        };
        
        window.clearOutput = function() {
            document.getElementById('output').textContent = '等待测试...';
        };
    </script>
</body>
</html>
