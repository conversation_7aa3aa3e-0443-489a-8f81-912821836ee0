import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import naive from 'naive-ui'
import App from './App.vue'

// 导入样式
import 'uno.css'
import './styles/main.css'

// 通用字体
const meta = document.createElement('meta')
meta.name = 'naive-ui-style'
document.head.appendChild(meta)

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('./views/ChatView.vue')
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('./views/SettingsView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()
const app = createApp(App)

console.log('🚀 应用开始初始化...')
app.use(pinia)
console.log('✅ Pinia 已加载')
app.use(router)
console.log('✅ Router 已加载')
app.use(naive)
console.log('✅ Naive UI 已加载')

console.log('🎯 准备挂载应用到 #app')
app.mount('#app')
console.log('✅ 应用已成功挂载！')
