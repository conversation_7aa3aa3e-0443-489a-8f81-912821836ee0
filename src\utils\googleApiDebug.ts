// Google API调试工具
import type { AIProvider, ChatSettings } from '@/types'

export interface GoogleApiTestResult {
  success: boolean
  error?: string
  details?: any
  suggestions?: string[]
}

export class GoogleApiDebugger {
  private provider: AIProvider
  private settings: ChatSettings

  constructor(provider: AIProvider, settings: ChatSettings) {
    this.provider = provider
    this.settings = settings
  }

  // 测试网络连接
  async testNetworkConnectivity(): Promise<GoogleApiTestResult> {
    try {
      console.log('🌐 测试网络连接...')

      // 首先测试基本的网络连接
      const testUrls = [
        'https://www.google.com',
        'https://generativelanguage.googleapis.com',
        this.provider.baseUrl
      ]

      for (const testUrl of testUrls) {
        try {
          console.log(`测试连接: ${testUrl}`)
          const response = await fetch(testUrl, {
            method: 'HEAD',
            mode: 'no-cors', // 避免CORS问题
            signal: AbortSignal.timeout(5000)
          })
          console.log(`✅ ${testUrl} 连接成功`)
        } catch (error) {
          console.log(`❌ ${testUrl} 连接失败:`, error)
          return {
            success: false,
            error: `无法连接到 ${testUrl}`,
            details: error,
            suggestions: [
              '检查网络连接是否正常',
              '确认防火墙没有阻止访问',
              '如果在中国大陆，可能需要使用VPN',
              '尝试更换网络环境'
            ]
          }
        }
      }

      return {
        success: true,
        details: { message: '网络连接正常' }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '网络测试失败',
        suggestions: [
          '检查网络连接',
          '确认DNS设置正确',
          '尝试重启网络连接'
        ]
      }
    }
  }

  // 测试API密钥有效性
  async testApiKey(): Promise<GoogleApiTestResult> {
    try {
      if (!this.provider.apiKey || !this.provider.apiKey.trim()) {
        return {
          success: false,
          error: 'API密钥未配置',
          suggestions: [
            '请在设置中配置Google API密钥',
            '访问 https://aistudio.google.com/ 获取API密钥'
          ]
        }
      }

      // 先测试网络连接
      const networkTest = await this.testNetworkConnectivity()
      if (!networkTest.success) {
        return {
          success: false,
          error: '网络连接失败: ' + networkTest.error,
          suggestions: networkTest.suggestions
        }
      }

      // 测试获取模型列表
      const url = `${this.provider.baseUrl}/models?key=${this.provider.apiKey}`
      console.log('测试Google API密钥，URL:', url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Desktop-Client/1.0.0'
        },
        signal: AbortSignal.timeout(10000) // 10秒超时
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Google API密钥测试失败:', errorText)
        
        let suggestions = []
        if (response.status === 400) {
          suggestions.push('API密钥格式可能不正确')
          suggestions.push('确保API密钥没有多余的空格或字符')
        } else if (response.status === 403) {
          suggestions.push('API密钥可能无效或已过期')
          suggestions.push('检查API密钥是否有正确的权限')
          suggestions.push('确认API密钥对应的项目已启用Gemini API')
        } else if (response.status === 429) {
          suggestions.push('API请求频率过高，请稍后重试')
        }

        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
          details: errorText,
          suggestions
        }
      }

      const data = await response.json()
      console.log('Google API密钥测试成功，可用模型:', data.models?.length || 0)

      return {
        success: true,
        details: {
          availableModels: data.models?.length || 0,
          models: data.models?.slice(0, 5).map((m: any) => m.name) || []
        }
      }

    } catch (error) {
      console.error('Google API密钥测试异常:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        suggestions: [
          '检查网络连接',
          '确认API密钥格式正确',
          '尝试重新获取API密钥'
        ]
      }
    }
  }

  // 测试简单对话
  async testSimpleChat(): Promise<GoogleApiTestResult> {
    try {
      const apiKeyTest = await this.testApiKey()
      if (!apiKeyTest.success) {
        return apiKeyTest
      }

      const modelName = this.provider.models[0]?.name || 'gemini-pro'
      const url = `${this.provider.baseUrl}/models/${modelName}:generateContent?key=${this.provider.apiKey}`

      const requestBody = {
        contents: [{
          role: 'user',
          parts: [{ text: '你好，请回复"测试成功"' }]
        }],
        generationConfig: {
          temperature: 0.1,
          maxOutputTokens: 100,
          candidateCount: 1
        }
      }

      console.log('测试Google简单对话，URL:', url)
      console.log('请求体:', JSON.stringify(requestBody, null, 2))

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AI-Desktop-Client/1.0.0'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Google简单对话测试失败:', errorText)
        
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`,
          details: errorText,
          suggestions: [
            '检查模型名称是否正确',
            '确认请求格式符合Google API规范',
            '检查API配额是否充足'
          ]
        }
      }

      const data = await response.json()
      console.log('Google简单对话测试响应:', data)

      if (data.error) {
        return {
          success: false,
          error: `Google API错误: ${data.error.message}`,
          details: data.error,
          suggestions: [
            '检查请求内容是否违反安全策略',
            '尝试修改请求内容',
            '检查API配额和限制'
          ]
        }
      }

      if (data.candidates && data.candidates[0] && data.candidates[0].content) {
        const candidate = data.candidates[0]
        const content = candidate.content.parts
          ?.filter((part: any) => part.text)
          ?.map((part: any) => part.text)
          ?.join('') || ''

        return {
          success: true,
          details: {
            response: content,
            finishReason: candidate.finishReason,
            safetyRatings: candidate.safetyRatings
          }
        }
      }

      return {
        success: false,
        error: '响应格式异常',
        details: data,
        suggestions: [
          '检查API响应格式',
          '确认使用的模型支持当前请求'
        ]
      }

    } catch (error) {
      console.error('Google简单对话测试异常:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        suggestions: [
          '检查网络连接',
          '确认请求格式正确',
          '检查API服务状态'
        ]
      }
    }
  }

  // 生成诊断报告
  async generateDiagnosticReport(): Promise<{
    apiKeyTest: GoogleApiTestResult
    chatTest: GoogleApiTestResult
    configuration: any
    recommendations: string[]
  }> {
    console.log('🔍 开始Google API诊断...')

    const apiKeyTest = await this.testApiKey()
    const chatTest = await this.testSimpleChat()

    const configuration = {
      providerId: this.provider.id,
      providerName: this.provider.displayName,
      enabled: this.provider.enabled,
      baseUrl: this.provider.baseUrl,
      hasApiKey: !!this.provider.apiKey,
      apiKeyLength: this.provider.apiKey?.length || 0,
      modelCount: this.provider.models.length,
      selectedModels: this.provider.selectedModels || [],
      settings: this.settings
    }

    const recommendations = []

    if (!apiKeyTest.success) {
      recommendations.push('首先解决API密钥问题')
      recommendations.push(...(apiKeyTest.suggestions || []))
    }

    if (!chatTest.success && apiKeyTest.success) {
      recommendations.push('API密钥有效但对话测试失败，检查请求格式')
      recommendations.push(...(chatTest.suggestions || []))
    }

    if (apiKeyTest.success && chatTest.success) {
      recommendations.push('Google API配置正常，如仍有问题请检查应用内的调用逻辑')
    }

    const result = {
      apiKeyTest,
      chatTest,
      configuration,
      recommendations
    }

    // 将诊断结果写入文件
    await this.saveDiagnosticToFile(result)

    return result
  }

  // 将诊断结果保存到文件
  private async saveDiagnosticToFile(result: any) {
    try {
      const timestamp = new Date().toLocaleString('zh-CN')
      const reportContent = this.generateReportContent(result, timestamp)

      // 直接使用浏览器下载，避免Tauri API导入问题
      this.downloadAsFile(reportContent)
    } catch (error) {
      console.error('保存诊断报告失败:', error)
    }
  }

  // 浏览器下载文件
  private downloadAsFile(content: string) {
    const blob = new Blob([content], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `google_api_diagnostic_${Date.now()}.md`
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    console.log('📄 诊断报告已下载')
  }

  // 生成报告内容
  private generateReportContent(result: any, timestamp: string): string {
    const { apiKeyTest, chatTest, configuration, recommendations } = result

    return `# Google API 诊断报告

## 📊 基本信息
- **诊断时间**: ${timestamp}
- **提供商**: ${configuration.providerName}
- **基础URL**: ${configuration.baseUrl}
- **启用状态**: ${configuration.enabled ? '已启用' : '未启用'}
- **API密钥**: ${configuration.hasApiKey ? '已配置' : '未配置'}
- **模型数量**: ${configuration.modelCount}

## 🔍 诊断结果

### API密钥测试
- **状态**: ${apiKeyTest.success ? '✅ 通过' : '❌ 失败'}
- **错误**: ${apiKeyTest.error || '无'}
${apiKeyTest.details ? `- **详细信息**: ${JSON.stringify(apiKeyTest.details, null, 2)}` : ''}
${apiKeyTest.suggestions ? `- **建议**:\n${apiKeyTest.suggestions.map(s => `  - ${s}`).join('\n')}` : ''}

### 对话测试
- **状态**: ${chatTest.success ? '✅ 通过' : '❌ 失败'}
- **错误**: ${chatTest.error || '无'}
${chatTest.details ? `- **详细信息**: ${JSON.stringify(chatTest.details, null, 2)}` : ''}
${chatTest.suggestions ? `- **建议**:\n${chatTest.suggestions.map(s => `  - ${s}`).join('\n')}` : ''}

## 💡 推荐操作
${recommendations.map(r => `- ${r}`).join('\n')}

## 🔧 配置信息
\`\`\`json
${JSON.stringify(configuration, null, 2)}
\`\`\`

---
*此报告由AI桌面客户端自动生成*
`
  }
}

// 便捷函数
export async function debugGoogleApi(provider: AIProvider, settings: ChatSettings) {
  const apiDebugger = new GoogleApiDebugger(provider, settings)
  return await apiDebugger.generateDiagnosticReport()
}
