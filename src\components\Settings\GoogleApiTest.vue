<template>
  <div class="google-api-test">
    <n-card title="Google API 诊断工具" size="small">
      <template #header-extra>
        <n-button 
          type="primary" 
          size="small" 
          :loading="testing"
          @click="runDiagnostic"
        >
          {{ testing ? '诊断中...' : '开始诊断' }}
        </n-button>
      </template>

      <div class="test-content">
        <n-alert 
          v-if="!googleProvider?.enabled"
          type="warning" 
          title="Google Gemini 未启用"
          style="margin-bottom: 16px"
        >
          请先在设置中启用 Google Gemini 提供商
        </n-alert>

        <n-alert 
          v-if="googleProvider?.enabled && !googleProvider?.apiKey"
          type="error" 
          title="API密钥未配置"
          style="margin-bottom: 16px"
        >
          请在设置中配置 Google API 密钥
        </n-alert>

        <div v-if="diagnosticResult" class="diagnostic-results">
          <n-divider title-placement="left">诊断结果</n-divider>
          
          <!-- API密钥测试结果 -->
          <div class="test-item">
            <n-tag 
              :type="diagnosticResult.apiKeyTest.success ? 'success' : 'error'"
              size="small"
            >
              API密钥测试: {{ diagnosticResult.apiKeyTest.success ? '通过' : '失败' }}
            </n-tag>
            
            <div v-if="!diagnosticResult.apiKeyTest.success" class="error-details">
              <p><strong>错误:</strong> {{ diagnosticResult.apiKeyTest.error }}</p>
              <div v-if="diagnosticResult.apiKeyTest.suggestions?.length">
                <strong>建议:</strong>
                <ul>
                  <li v-for="suggestion in diagnosticResult.apiKeyTest.suggestions" :key="suggestion">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>
            
            <div v-if="diagnosticResult.apiKeyTest.success && diagnosticResult.apiKeyTest.details" class="success-details">
              <p>可用模型数量: {{ diagnosticResult.apiKeyTest.details.availableModels }}</p>
              <p v-if="diagnosticResult.apiKeyTest.details.models?.length">
                示例模型: {{ diagnosticResult.apiKeyTest.details.models.join(', ') }}
              </p>
            </div>
          </div>

          <!-- 对话测试结果 -->
          <div class="test-item">
            <n-tag 
              :type="diagnosticResult.chatTest.success ? 'success' : 'error'"
              size="small"
            >
              对话测试: {{ diagnosticResult.chatTest.success ? '通过' : '失败' }}
            </n-tag>
            
            <div v-if="!diagnosticResult.chatTest.success" class="error-details">
              <p><strong>错误:</strong> {{ diagnosticResult.chatTest.error }}</p>
              <div v-if="diagnosticResult.chatTest.suggestions?.length">
                <strong>建议:</strong>
                <ul>
                  <li v-for="suggestion in diagnosticResult.chatTest.suggestions" :key="suggestion">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>
            
            <div v-if="diagnosticResult.chatTest.success && diagnosticResult.chatTest.details" class="success-details">
              <p><strong>AI回复:</strong> {{ diagnosticResult.chatTest.details.response }}</p>
              <p><strong>结束原因:</strong> {{ diagnosticResult.chatTest.details.finishReason }}</p>
            </div>
          </div>

          <!-- 配置信息 -->
          <div class="test-item">
            <n-divider title-placement="left">配置信息</n-divider>
            <div class="config-info">
              <p><strong>提供商:</strong> {{ diagnosticResult.configuration.providerName }}</p>
              <p><strong>基础URL:</strong> {{ diagnosticResult.configuration.baseUrl }}</p>
              <p><strong>启用状态:</strong> {{ diagnosticResult.configuration.enabled ? '已启用' : '未启用' }}</p>
              <p><strong>API密钥:</strong> {{ diagnosticResult.configuration.hasApiKey ? '已配置' : '未配置' }}</p>
              <p><strong>模型数量:</strong> {{ diagnosticResult.configuration.modelCount }}</p>
            </div>
          </div>

          <!-- 推荐操作 -->
          <div class="test-item">
            <n-divider title-placement="left">推荐操作</n-divider>
            <div class="recommendations">
              <ul>
                <li v-for="recommendation in diagnosticResult.recommendations" :key="recommendation">
                  {{ recommendation }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div v-if="testing" class="testing-indicator">
          <n-spin size="small" />
          <span style="margin-left: 8px">正在进行诊断测试...</span>
        </div>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NCard, NButton, NAlert, NTag, NDivider, NSpin } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'
import { debugGoogleApi } from '@/utils/googleApiDebug'

const settingsStore = useSettingsStore()

const testing = ref(false)
const diagnosticResult = ref<any>(null)

const googleProvider = computed(() => {
  return settingsStore.providers.find(p => p.id === 'google')
})

const runDiagnostic = async () => {
  if (!googleProvider.value) {
    console.error('Google提供商未找到')
    return
  }

  testing.value = true
  diagnosticResult.value = null

  try {
    console.log('🔍 开始Google API诊断...')
    const result = await debugGoogleApi(googleProvider.value, settingsStore.chat)
    diagnosticResult.value = result
    console.log('📊 诊断结果:', result)
  } catch (error) {
    console.error('诊断过程出错:', error)
    diagnosticResult.value = {
      apiKeyTest: { success: false, error: '诊断过程出错: ' + (error as Error).message },
      chatTest: { success: false, error: '未执行' },
      configuration: {},
      recommendations: ['检查网络连接', '查看浏览器控制台获取详细错误信息']
    }
  } finally {
    testing.value = false
  }
}
</script>

<style scoped>
.google-api-test {
  max-width: 800px;
}

.test-content {
  min-height: 200px;
}

.diagnostic-results {
  margin-top: 16px;
}

.test-item {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-color);
}

.error-details {
  margin-top: 8px;
  color: var(--error-color);
}

.success-details {
  margin-top: 8px;
  color: var(--success-color);
}

.config-info p {
  margin: 4px 0;
}

.recommendations ul {
  margin: 8px 0;
  padding-left: 20px;
}

.recommendations li {
  margin: 4px 0;
}

.testing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--text-color-2);
}
</style>
