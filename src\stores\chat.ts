import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Conversation, ConversationGroup, Message, Assistant, ChatSettings } from '@/types'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const conversations = ref<Conversation[]>([])
  const conversationGroups = ref<ConversationGroup[]>([])
  const currentConversation = ref<Conversation | null>(null)
  const currentGroup = ref<ConversationGroup | null>(null)
  const messages = ref<Message[]>([])
  const isLoading = ref(false)
  const isStreaming = ref(false)
  const streamingMessageId = ref<string | null>(null)

  // 每个对话的流式状态和加载状态 - 高性能并发支持
  const conversationStreamingStates = ref<Map<string, {
    isStreaming: boolean
    streamingMessageId: string | null
    isLoading: boolean
    abortController?: AbortController
  }>>(new Map())

  // 防抖保存定时器
  const saveTimers = new Map<string, NodeJS.Timeout>()

  // 计算属性
  const sortedConversations = computed(() => {
    return [...conversations.value].sort((a, b) => {
      // 置顶的对话排在前面
      if (a.isPinned && !b.isPinned) return -1
      if (!a.isPinned && b.isPinned) return 1

      // 按更新时间排序
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    })
  })

  // 排序后的分组 - 修复排序逻辑
  const sortedGroups = computed(() => {
    return [...conversationGroups.value].sort((a, b) => {
      // 按创建时间正序排列，新创建的分组排在后面
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    })
  })

  // 按分组组织的对话 - 修复置顶排序
  const conversationsByGroup = computed(() => {
    const grouped: Record<string, Conversation[]> = {}
    const ungrouped: Conversation[] = []

    // 使用排序后的对话列表，确保置顶对话排在前面
    sortedConversations.value.forEach(conversation => {
      if (conversation.groupId) {
        if (!grouped[conversation.groupId]) {
          grouped[conversation.groupId] = []
        }
        grouped[conversation.groupId].push(conversation)
      } else {
        ungrouped.push(conversation)
      }
    })

    return { grouped, ungrouped }
  })

  // 获取分组信息
  const getGroupedConversations = computed(() => {
    const result: Array<{
      type: 'group' | 'conversation'
      group?: ConversationGroup
      conversation?: Conversation
      conversations?: Conversation[]
    }> = []

    const { grouped, ungrouped } = conversationsByGroup.value

    // 添加分组
    conversationGroups.value.forEach(group => {
      if (grouped[group.id] && grouped[group.id].length > 0) {
        result.push({
          type: 'group',
          group,
          conversations: grouped[group.id].sort((a, b) => 
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          )
        })
      }
    })

    // 添加未分组的对话
    ungrouped.forEach(conversation => {
      result.push({
        type: 'conversation',
        conversation
      })
    })

    return result
  })

  const currentMessages = computed(() => {
    if (!currentConversation.value) return []
    return messages.value.filter(m => m.conversationId === currentConversation.value!.id)
  })

  // 当前对话的流式状态
  const currentStreamingState = computed(() => {
    if (!currentConversation.value) return null
    return conversationStreamingStates.value.get(currentConversation.value.id) || null
  })

  // 智能全局状态同步函数 - 核心修复
  const updateGlobalState = (conversationId: string) => {
    if (currentConversation.value?.id === conversationId) {
      const state = conversationStreamingStates.value.get(conversationId)
      if (state) {
        isLoading.value = state.isLoading
        isStreaming.value = state.isStreaming
        streamingMessageId.value = state.streamingMessageId
      } else {
        isLoading.value = false
        isStreaming.value = false
        streamingMessageId.value = null
      }
    }
  }

  // 从存储加载数据
  const loadFromStorage = () => {
    try {
      console.log('📥 开始加载存储数据...')

      const storedConversations = localStorage.getItem('conversations')
      if (storedConversations) {
        const parsed = JSON.parse(storedConversations)
        conversations.value = parsed.map((conv: any) => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt)
        }))
        console.log('✅ 已加载', conversations.value.length, '个对话')
      } else {
        console.log('📭 未找到存储的对话数据')
      }

      const storedGroups = localStorage.getItem('conversationGroups')
      if (storedGroups) {
        const parsed = JSON.parse(storedGroups)
        conversationGroups.value = parsed.map((group: any) => ({
          ...group,
          createdAt: new Date(group.createdAt),
          updatedAt: new Date(group.updatedAt)
        }))
        console.log('✅ 已加载', conversationGroups.value.length, '个分组')
      } else {
        console.log('📭 未找到存储的分组数据')
      }

      const storedCurrentConv = localStorage.getItem('currentConversation')
      if (storedCurrentConv) {
        const parsed = JSON.parse(storedCurrentConv)
        const conversation = conversations.value.find(c => c.id === parsed.id)
        if (conversation) {
          currentConversation.value = conversation
        }
      }

      const storedCurrentGroup = localStorage.getItem('currentGroup')
      if (storedCurrentGroup) {
        const parsed = JSON.parse(storedCurrentGroup)
        const group = conversationGroups.value.find(g => g.id === parsed.id)
        if (group) {
          currentGroup.value = group
        }
      }
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }

  // 保存到存储
  const saveToStorage = () => {
    try {
      localStorage.setItem('conversations', JSON.stringify(conversations.value))
      localStorage.setItem('conversationGroups', JSON.stringify(conversationGroups.value))
      
      if (currentConversation.value) {
        localStorage.setItem('currentConversation', JSON.stringify(currentConversation.value))
      }
      
      if (currentGroup.value) {
        localStorage.setItem('currentGroup', JSON.stringify(currentGroup.value))
      }
    } catch (error) {
      console.error('保存数据失败:', error)
    }
  }

  // 选择对话 - 高性能稳定版本
  const selectConversation = async (conversationId: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (!conversation) return

    console.log('🔄 切换到对话:', conversation.title)

    // 第一步：强制保存当前对话的所有消息（包括流式消息）
    if (currentConversation.value && currentConversation.value.id !== conversationId) {
      console.log('💾 保存当前对话的所有消息...')
      
      // 获取当前对话的所有消息
      const currentMessages = messages.value.filter(m => m.conversationId === currentConversation.value!.id)
      
      // 立即保存到localStorage，不使用防抖
      if (currentMessages.length > 0) {
        try {
          const messagesWithTimestamp = currentMessages.map(msg => ({
            ...msg,
            timestamp: msg.timestamp || new Date()
          }))
          localStorage.setItem(`messages_${currentConversation.value!.id}`, JSON.stringify(messagesWithTimestamp))
          console.log('✅ 已保存', currentMessages.length, '条消息')
        } catch (error) {
          console.error('❌ 保存消息失败:', error)
        }
      }
      
      // 清除所有防抖定时器
      const existingTimer = saveTimers.get(currentConversation.value.id)
      if (existingTimer) {
        clearTimeout(existingTimer)
        saveTimers.delete(currentConversation.value.id)
      }
    }

    // 第二步：切换对话
    currentConversation.value = conversation

    // 第三步：智能更新全局状态 - 高性能稳定版本
    updateGlobalState(conversationId)
    console.log('🔄 切换对话状态更新完成，目标对话:', conversation.title)

    // 第四步：加载目标对话的消息
    await loadMessages(conversationId)
    
    console.log('✅ 对话切换完成，当前消息数:', messages.value.filter(m => m.conversationId === conversationId).length)
  }

  // 选择分组
  const selectGroup = (groupId: string | null) => {
    if (groupId) {
      const group = conversationGroups.value.find(g => g.id === groupId)
      if (group) {
        currentGroup.value = group
        console.log('🔄 切换到分组:', group.name)
      }
    } else {
      currentGroup.value = null
      console.log('🔄 切换到未分组')
    }
    saveToStorage()
  }

  // 更新对话
  const updateConversation = (conversationId: string, updates: Partial<Conversation>, updateTimestamp: boolean = true) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index !== -1) {
      conversations.value[index] = {
        ...conversations.value[index],
        ...updates,
        ...(updateTimestamp && { updatedAt: new Date() })
      }
      saveToStorage()
    }
  }

  // 更新对话的聊天设置（不影响排序）
  const updateConversationChatSettings = (conversationId: string, settings: Partial<ChatSettings>) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index !== -1) {
      const conversation = conversations.value[index]
      conversation.chatSettings = {
        ...conversation.chatSettings,
        ...settings
      }
      // 注意：不更新 updatedAt，避免影响对话排序

      // 如果是当前对话，同步更新引用
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value.chatSettings = conversation.chatSettings
      }

      saveToStorage()
      console.log('✅ 已更新对话聊天设置（不影响排序）:', conversationId, settings)
    }
  }

  // 获取对话的聊天设置
  const getConversationChatSettings = (conversationId: string): ChatSettings | null => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    return conversation?.chatSettings || null
  }

  // 重置对话的聊天设置为默认值
  const resetConversationChatSettings = (conversationId: string, defaultSettings?: ChatSettings) => {
    const resetSettings = defaultSettings || {
      temperature: 0.7,
      maxTokens: 2048,
      topP: 1.0,
      frequencyPenalty: 0,
      presencePenalty: 0,
      systemPrompt: '你是一个有用的AI助手，请用中文回答问题。'
    }

    updateConversationChatSettings(conversationId, resetSettings)
  }

  // 删除对话
  const deleteConversation = (conversationId: string) => {
    const index = conversations.value.findIndex(c => c.id === conversationId)
    if (index !== -1) {
      conversations.value.splice(index, 1)

      // 删除对话的消息
      localStorage.removeItem(`messages_${conversationId}`)

      // 如果删除的是当前对话，清空当前对话
      if (currentConversation.value?.id === conversationId) {
        currentConversation.value = null
        messages.value = []
      }

      saveToStorage()
    }
  }

  // 切换对话置顶状态
  const togglePinConversation = (conversationId: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.isPinned = !conversation.isPinned
      conversation.updatedAt = new Date()
      console.log('📌 切换置顶状态:', conversation.title, '置顶:', conversation.isPinned)
      saveToStorage()
    }
  }

  // 创建新对话
  const createConversation = (title?: string, groupId?: string, model?: string, inheritSettings?: ChatSettings): Conversation => {
    const conversation: Conversation = {
      id: crypto.randomUUID(),
      title: title || '新对话',
      createdAt: new Date(),
      updatedAt: new Date(),
      messageCount: 0,
      groupId,
      model: model || 'deepseek-reasoner',
      isPinned: false,
      // 使用传入的设置或默认值
      chatSettings: inheritSettings || {
        temperature: 0.7,
        maxTokens: 2048,
        topP: 1.0,
        frequencyPenalty: 0,
        presencePenalty: 0,
        systemPrompt: '你是一个有用的AI助手，请用中文回答问题。'
      }
    }

    conversations.value.unshift(conversation)
    console.log('✅ 创建新对话:', conversation.title, '分组:', groupId || '无')
    saveToStorage()
    return conversation
  }

  // 添加消息 - 修复跨对话消息显示问题
  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date()
    }

    // 如果是当前对话，添加到内存
    if (currentConversation.value?.id === message.conversationId) {
      messages.value.push(newMessage)
      console.log('✅ 消息已添加到当前对话内存')
    }

    // 始终保存到localStorage（支持跨对话）
    saveMessageToStorage(message.conversationId, newMessage)
    console.log('✅ 消息已保存到存储:', message.conversationId)

    // 更新对话消息计数和时间 - 修复计数问题
    // 使用延迟确保localStorage写入完成
    setTimeout(() => {
      const messageCount = getConversationMessageCount(message.conversationId)
      console.log('📊 更新对话消息计数:', message.conversationId, '数量:', messageCount)
      updateConversation(message.conversationId, {
        messageCount: messageCount,
        updatedAt: new Date()
      })
    }, 10)

    return newMessage
  }

  // 获取对话的消息数量（从存储中计算）
  const getConversationMessageCount = (conversationId: string): number => {
    try {
      const stored = localStorage.getItem(`messages_${conversationId}`)
      if (stored) {
        const storedMessages = JSON.parse(stored)
        return storedMessages.length
      }
      return 0
    } catch (error) {
      console.error('获取消息数量失败:', error)
      return 0
    }
  }

  // 高性能并发安全的消息更新函数 - 核心实现
  const updateStreamingMessage = async (conversationId: string, messageId: string, chunk: any) => {
    try {
      // 策略1：优先尝试内存更新（高性能）
      const memoryIndex = messages.value.findIndex(m => m.id === messageId)
      let updatedMessage: Message | null = null

      if (memoryIndex !== -1) {
        // 消息在当前对话的内存中，直接更新
        if (chunk.isComplete) {
          // 流式完成
          messages.value[memoryIndex] = {
            ...messages.value[memoryIndex],
            model: chunk.model,
            tokens: chunk.tokens,
            reasoningComplete: true
          }
          updatedMessage = messages.value[memoryIndex]
          console.log('✅ 内存流式完成，最终长度:', updatedMessage.content.length)
        } else {
          // 更新内容和思考过程
          const updates: any = {}

          if (chunk.content) {
            updates.content = messages.value[memoryIndex].content + chunk.content
          }

          if (chunk.reasoning !== undefined) {
            updates.reasoning = chunk.reasoning
            updates.isReasoningModel = true
          }

          if (chunk.reasoningComplete !== undefined) {
            updates.reasoningComplete = chunk.reasoningComplete
          }

          messages.value[memoryIndex] = {
            ...messages.value[memoryIndex],
            ...updates
          }
          updatedMessage = messages.value[memoryIndex]
          console.log('📝 内存更新:', chunk.reasoning ? '思考过程' : '内容', '当前长度:', updatedMessage.content.length)
        }
      } else {
        // 策略2：消息不在内存中，从存储更新（跨对话场景）
        console.log('🔄 跨对话更新，从存储操作')
        const stored = localStorage.getItem(`messages_${conversationId}`)
        if (stored) {
          const storedMessages = JSON.parse(stored)
          const storageIndex = storedMessages.findIndex((m: any) => m.id === messageId)

          if (storageIndex !== -1) {
            if (chunk.isComplete) {
              // 流式完成
              storedMessages[storageIndex] = {
                ...storedMessages[storageIndex],
                model: chunk.model,
                tokens: chunk.tokens,
                reasoningComplete: true
              }
              updatedMessage = storedMessages[storageIndex]
              console.log('✅ 存储流式完成，最终长度:', updatedMessage.content.length)
            } else {
              // 更新内容和思考过程
              const updates: any = {}

              if (chunk.content) {
                updates.content = storedMessages[storageIndex].content + chunk.content
              }

              if (chunk.reasoning !== undefined) {
                updates.reasoning = chunk.reasoning
                updates.isReasoningModel = true
              }

              if (chunk.reasoningComplete !== undefined) {
                updates.reasoningComplete = chunk.reasoningComplete
              }

              storedMessages[storageIndex] = {
                ...storedMessages[storageIndex],
                ...updates
              }
              updatedMessage = storedMessages[storageIndex]
              console.log('📝 存储更新:', chunk.reasoning ? '思考过程' : '内容', '当前长度:', updatedMessage.content.length)
            }

            // 保存回存储
            localStorage.setItem(`messages_${conversationId}`, JSON.stringify(storedMessages))
          }
        }
      }

      // 策略3：状态管理和同步
      if (updatedMessage) {
        // 如果不是内存更新，确保存储同步
        if (memoryIndex === -1) {
          // 跨对话场景，只更新存储
        } else {
          // 当前对话场景，同时保存到存储
          saveMessageToStorage(conversationId, updatedMessage)
        }

        // 更新对话级状态
        if (chunk.isComplete) {
          const streamingState = conversationStreamingStates.value.get(conversationId)
          if (streamingState) {
            streamingState.isStreaming = false
            streamingState.streamingMessageId = null
            streamingState.isLoading = false
          }
        }

        // 智能更新全局状态
        updateGlobalState(conversationId)
      }

    } catch (error) {
      console.error('❌ 消息更新失败:', error)
    }
  }

  // 保存单个消息到存储 - 增强版本
  const saveMessageToStorage = (conversationId: string, message: Message) => {
    try {
      const stored = localStorage.getItem(`messages_${conversationId}`)
      let storedMessages = stored ? JSON.parse(stored) : []

      const index = storedMessages.findIndex((m: any) => m.id === message.id)
      if (index !== -1) {
        // 更新现有消息
        storedMessages[index] = message
        console.log('📝 更新存储中的消息:', message.id)
      } else {
        // 添加新消息
        storedMessages.push(message)
        console.log('➕ 添加新消息到存储:', message.id, '对话:', conversationId)
      }

      localStorage.setItem(`messages_${conversationId}`, JSON.stringify(storedMessages))
      console.log('💾 消息已保存，对话', conversationId, '共', storedMessages.length, '条消息')
    } catch (error) {
      console.error('保存消息失败:', error)
    }
  }

  // 发送到AI服务 - 高性能稳定版本
  const sendToAI = async (userMessage: Message) => {
    const conversationId = userMessage.conversationId
    const aiMessageId = crypto.randomUUID()

    // 设置对话级状态（支持并发）
    conversationStreamingStates.value.set(conversationId, {
      isStreaming: true,
      streamingMessageId: aiMessageId,
      isLoading: true,
      abortController: new AbortController()
    })

    // 智能更新全局状态（保持兼容性）
    updateGlobalState(conversationId)

    // 创建AI回复消息占位符（稍后会更新isReasoningModel）
    const aiMessage: Message = {
      id: aiMessageId,
      role: 'assistant',
      content: '',
      conversationId,
      timestamp: new Date(),
      isReasoningModel: false,
      reasoningComplete: false
    }

    // 添加到消息列表和存储
    if (currentConversation.value?.id === conversationId) {
      messages.value.push(aiMessage)
      console.log('✅ AI消息已添加到当前对话内存')
    }

    // 保存AI消息到存储
    saveMessageToStorage(conversationId, aiMessage)
    console.log('✅ AI消息已保存到存储:', conversationId)

    try {
      // 导入AI服务 - 恢复真实功能
      const aiServiceModule = await import('../services/aiService')
      const settingsModule = await import('./settings')

      const { AIServiceFactory } = aiServiceModule
      const { useSettingsStore } = settingsModule
      const settingsStore = useSettingsStore()

      // 获取当前对话选择的模型
      const currentConv = conversations.value.find((c: any) => c.id === userMessage.conversationId)
      let selectedModelId = currentConv?.model

      // 如果没有选择模型，找到第一个启用的提供商的第一个模型作为默认
      if (!selectedModelId) {
        for (const provider of settingsStore.providers) {
          if (provider.enabled && provider.models.length > 0) {
            selectedModelId = provider.models[0].id
            console.log('🎯 使用默认模型:', selectedModelId, '来自提供商:', provider.displayName)
            break
          }
        }
      }

      console.log('🔍 选择的模型ID:', selectedModelId)

      // 根据模型ID找到对应的提供商和模型
      let targetProvider: any = null
      let targetModel: any = null

      for (const provider of settingsStore.providers) {
        if (provider.enabled) {
          const model = provider.models.find((m: any) => m.id === selectedModelId)
          if (model) {
            targetProvider = provider
            targetModel = model
            break
          }
        }
      }

      console.log('🎯 找到目标提供商:', targetProvider?.displayName, '模型:', targetModel?.displayName)
      console.log('🔑 API密钥状态:', targetProvider?.apiKey ? '已配置' : '未配置', '长度:', targetProvider?.apiKey?.length)
      console.log('🌐 基础URL:', targetProvider?.baseUrl)

      if (!targetProvider || !targetModel) {
        throw new Error(`未找到模型 ${selectedModelId} 或其提供商未启用`)
      }

      console.log('🤖 使用AI服务:', targetProvider.name, '-', targetModel.name)

      // 更新AI消息的思考模型标识
      if (targetModel.isReasoningModel) {
        aiMessage.isReasoningModel = true
        // 同步更新内存中的消息
        if (currentConversation.value?.id === conversationId) {
          const messageIndex = messages.value.findIndex(m => m.id === aiMessageId)
          if (messageIndex !== -1) {
            messages.value[messageIndex].isReasoningModel = true
          }
        }
        // 同步更新存储中的消息
        saveMessageToStorage(conversationId, aiMessage)
        console.log('🧠 标识为思考模型消息')
      }

      // 获取对话级的聊天设置，如果没有则使用全局设置
      const conversationChatSettings = currentConv?.chatSettings || settingsStore.chat
      console.log('🔧 使用对话级参数:', conversationChatSettings)

      // 创建AI服务实例，使用对话级设置
      const aiService = AIServiceFactory.createService(
        targetProvider,
        conversationChatSettings,
        targetModel.id
      )

      // 获取对话历史
      const conversationMessages = messages.value.filter(
        m => m.conversationId === userMessage.conversationId
      )

      // 流式处理AI回复
      for await (const chunk of aiService.sendChatStream(conversationMessages)) {
        console.log('📝 收到流式数据块:', chunk.content, '长度:', chunk.content?.length, '完成:', chunk.isComplete)

        // 高性能并发安全的消息更新
        await updateStreamingMessage(conversationId, aiMessageId, chunk)
      }

    } catch (error) {
      console.error('AI回复失败:', error)

      // 更新为错误消息
      const messageIndex = messages.value.findIndex(m => m.id === aiMessageId)
      if (messageIndex !== -1) {
        messages.value[messageIndex] = {
          ...messages.value[messageIndex],
          content: '抱歉，发生了错误，请稍后重试。'
        }
      }
    } finally {
      // 清理状态
      const streamingState = conversationStreamingStates.value.get(conversationId)
      if (streamingState) {
        streamingState.isStreaming = false
        streamingState.streamingMessageId = null
        streamingState.isLoading = false
      }

      // 更新全局状态
      updateGlobalState(conversationId)

      // 更新对话消息计数 - 使用存储中的准确计数
      setTimeout(() => {
        const messageCount = getConversationMessageCount(conversationId)
        console.log('📊 AI完成后更新消息计数:', conversationId, '数量:', messageCount)
        updateConversation(conversationId, {
          messageCount: messageCount
        })
      }, 50)

      saveToStorage()
    }
  }

  // 发送消息 - 高性能稳定版本
  const sendMessage = async (content: string) => {
    if (!currentConversation.value) {
      throw new Error('没有选中的对话')
    }

    // 添加用户消息
    const userMessage = addMessage({
      role: 'user',
      content,
      conversationId: currentConversation.value.id
    })

    // 发送到AI
    await sendToAI(userMessage)
  }

  // 加载消息 - 修复版本
  const loadMessages = async (conversationId: string) => {
    console.log('📥 加载对话消息:', conversationId)

    try {
      // 从localStorage加载消息
      const stored = localStorage.getItem(`messages_${conversationId}`)
      if (stored) {
        const storedMessages = JSON.parse(stored)
        const messagesWithDates = storedMessages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }))

        // 如果是当前对话，加载到内存并清空旧消息
        if (currentConversation.value?.id === conversationId) {
          messages.value = messagesWithDates
          console.log('✅ 已加载', messagesWithDates.length, '条消息到内存')
          console.log('📋 消息列表:', messagesWithDates.map(m => `${m.role}: ${m.content.substring(0, 50)}...`))
        }

        return messagesWithDates
      }

      // 如果是当前对话且没有存储消息，清空内存
      if (currentConversation.value?.id === conversationId) {
        messages.value = []
        console.log('📭 该对话暂无消息，已清空内存')
      }

      return []
    } catch (error) {
      console.error('加载消息失败:', error)
      // 如果是当前对话，确保清空内存
      if (currentConversation.value?.id === conversationId) {
        messages.value = []
      }
      return []
    }
  }

  // 跨对话消息更新函数 - 核心修复
  const updateCrossConversationMessage = async (conversationId: string, messageId: string, chunk: any) => {
    try {
      console.log('🔄 跨对话消息更新:', conversationId, messageId)

      // 从localStorage读取消息
      const stored = localStorage.getItem(`messages_${conversationId}`)
      if (!stored) {
        console.warn('⚠️ 未找到对话消息存储:', conversationId)
        return
      }

      const storedMessages = JSON.parse(stored)
      const messageIndex = storedMessages.findIndex((m: any) => m.id === messageId)

      if (messageIndex === -1) {
        console.warn('⚠️ 未找到目标消息:', messageId)
        return
      }

      // 更新消息
      if (chunk.isComplete) {
        // 流式完成 - 只更新元数据，不追加内容
        storedMessages[messageIndex] = {
          ...storedMessages[messageIndex],
          model: chunk.model,
          tokens: chunk.tokens
        }
        console.log('✅ 跨对话流式完成，最终长度:', storedMessages[messageIndex].content.length)

        // 标记流式状态和加载状态结束
        const streamingState = conversationStreamingStates.value.get(conversationId)
        if (streamingState) {
          streamingState.isStreaming = false
          streamingState.streamingMessageId = null
          streamingState.isLoading = false
        }

        // 如果是当前对话，更新全局状态
        if (currentConversation.value?.id === conversationId) {
          isStreaming.value = false
          streamingMessageId.value = null
          isLoading.value = false
        }
      } else {
        // 追加内容
        storedMessages[messageIndex] = {
          ...storedMessages[messageIndex],
          content: storedMessages[messageIndex].content + chunk.content
        }
        console.log('📝 跨对话追加内容，当前长度:', storedMessages[messageIndex].content.length)
      }

      // 保存回localStorage
      localStorage.setItem(`messages_${conversationId}`, JSON.stringify(storedMessages))

      // 如果更新的是当前对话，同步到内存
      if (currentConversation.value?.id === conversationId) {
        const currentMessageIndex = messages.value.findIndex(m => m.id === messageId)
        if (currentMessageIndex !== -1) {
          messages.value[currentMessageIndex] = storedMessages[messageIndex]
          console.log('🔄 同步到当前对话内存')
        }
      }

      // 更新对话消息计数
      const totalMessages = storedMessages.length
      updateConversation(conversationId, {
        messageCount: totalMessages
      })

    } catch (error) {
      console.error('跨对话消息更新失败:', error)
    }
  }

  // 重新生成消息
  const regenerateMessage = async (messageId: string) => {
    const messageIndex = messages.value.findIndex(m => m.id === messageId)
    if (messageIndex === -1) return

    const message = messages.value[messageIndex]
    if (message.role !== 'assistant') return

    // 找到前一条用户消息
    const userMessageIndex = messageIndex - 1
    if (userMessageIndex < 0) return

    const userMessage = messages.value[userMessageIndex]
    if (userMessage.role !== 'user') return

    // 清空AI消息内容
    messages.value[messageIndex] = {
      ...message,
      content: '',
      model: undefined,
      tokens: undefined
    }

    // 重新发送到AI
    await sendToAI(userMessage)
  }

  // 分组管理 - 修复参数匹配
  const createGroup = (name: string, color?: string, icon?: string, description?: string): ConversationGroup => {
    const group: ConversationGroup = {
      id: crypto.randomUUID(),
      name,
      description,
      color: color || '#3b82f6',
      icon: icon || '📁',
      createdAt: new Date(),
      updatedAt: new Date(),
      conversationCount: 0,
      isCollapsed: false
    }

    conversationGroups.value.push(group)
    console.log('✅ 创建分组成功:', group.name, '颜色:', group.color, '图标:', group.icon)
    saveToStorage()
    return group
  }

  const updateGroup = (groupId: string, updates: Partial<ConversationGroup>) => {
    const index = conversationGroups.value.findIndex(g => g.id === groupId)
    if (index !== -1) {
      conversationGroups.value[index] = {
        ...conversationGroups.value[index],
        ...updates,
        updatedAt: new Date()
      }
      saveToStorage()
    }
  }

  const deleteGroup = (groupId: string) => {
    // 将分组内的对话移到未分组
    conversations.value.forEach(conversation => {
      if (conversation.groupId === groupId) {
        conversation.groupId = undefined
      }
    })

    // 删除分组
    const index = conversationGroups.value.findIndex(g => g.id === groupId)
    if (index !== -1) {
      conversationGroups.value.splice(index, 1)
      saveToStorage()
    }
  }

  const moveConversationToGroup = (conversationId: string, groupId?: string) => {
    const conversation = conversations.value.find(c => c.id === conversationId)
    if (conversation) {
      conversation.groupId = groupId
      conversation.updatedAt = new Date()
      saveToStorage()
    }
  }

  // 切换分组折叠状态
  const toggleGroupCollapse = (groupId: string) => {
    const group = conversationGroups.value.find(g => g.id === groupId)
    if (group) {
      group.isCollapsed = !group.isCollapsed
      saveToStorage()
    }
  }

  // 清空所有数据
  const clearAllData = () => {
    conversations.value = []
    conversationGroups.value = []
    currentConversation.value = null
    currentGroup.value = null
    messages.value = []
    conversationStreamingStates.value.clear()

    // 清空localStorage
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith('messages_') ||
          ['conversations', 'conversationGroups', 'currentConversation', 'currentGroup'].includes(key)) {
        localStorage.removeItem(key)
      }
    })
  }

  // 调试函数：检查localStorage内容
  const debugStorage = () => {
    console.log('🔍 localStorage调试信息:')
    console.log('conversations:', localStorage.getItem('conversations'))
    console.log('conversationGroups:', localStorage.getItem('conversationGroups'))
    console.log('currentConversation:', localStorage.getItem('currentConversation'))
    console.log('currentGroup:', localStorage.getItem('currentGroup'))

    // 检查消息存储
    const keys = Object.keys(localStorage)
    const messageKeys = keys.filter(key => key.startsWith('messages_'))
    console.log('消息存储键:', messageKeys)
  }



  // 初始化
  const initialize = () => {
    console.log('🚀 开始初始化chatStore...')
    debugStorage()
    loadFromStorage()

    // 如果没有任何数据，显示提示但不自动创建
    if (conversations.value.length === 0 && conversationGroups.value.length === 0) {
      console.log('📭 未找到任何数据，等待用户手动创建...')
    }

    // 如果有当前对话，加载其消息
    if (currentConversation.value) {
      loadMessages(currentConversation.value.id)
    }

    console.log('✅ chatStore初始化完成')
    console.log('当前状态:', {
      conversations: conversations.value.length,
      groups: conversationGroups.value.length,
      currentConversation: currentConversation.value?.title,
      currentGroup: currentGroup.value?.name
    })
  }

  return {
    // 状态
    conversations,
    conversationGroups,
    currentConversation,
    currentGroup,
    messages,
    isLoading,
    isStreaming,
    streamingMessageId,
    conversationStreamingStates,

    // 计算属性
    sortedConversations,
    sortedGroups,
    conversationsByGroup,
    getGroupedConversations,
    currentMessages,
    currentStreamingState,

    // 对话管理
    selectConversation,
    selectGroup,
    createConversation,
    updateConversation,
    deleteConversation,
    togglePinConversation,
    updateConversationChatSettings,
    getConversationChatSettings,
    resetConversationChatSettings,

    // 消息管理
    addMessage,
    sendMessage,
    loadMessages,
    regenerateMessage,
    updateStreamingMessage,
    updateCrossConversationMessage,

    // 分组管理
    createGroup,
    updateGroup,
    deleteGroup,
    moveConversationToGroup,
    toggleGroupCollapse,

    // 工具函数
    saveToStorage,
    loadFromStorage,
    clearAllData,
    initialize
  }
})
