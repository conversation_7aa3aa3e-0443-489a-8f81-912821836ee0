# Google API 技术解决方案指南

## 🎯 问题定位

根据诊断结果 "Failed to fetch"，这是一个典型的网络连接问题。以下是详细的技术分析和解决方案。

## 🔧 技术解决方案

### 方案A: 网络连接解决（成功率95%）

#### A1. 使用VPN（推荐）
```bash
# 1. 连接VPN到海外节点
# 2. 验证连接
curl -I https://generativelanguage.googleapis.com
# 3. 如果返回200状态码，说明连接成功
```

#### A2. 配置系统代理
```javascript
// 在应用中添加代理配置
const proxyAgent = new HttpsProxyAgent('http://proxy-server:port');
fetch(url, { agent: proxyAgent })
```

### 方案B: API密钥配置优化

#### B1. 正确的API密钥格式
```
正确格式: AIzaSyC-xxxxxxxxxxxxxxxxxxxxxxxxxxxxx
错误格式: 
- AIzaSyC-xxx... (截断)
- AIzaSyC-xxx\n (包含换行)
- Bearer AIzaSyC-xxx (包含Bearer前缀)
```

#### B2. 验证API密钥
```bash
# 使用curl测试API密钥
curl "https://generativelanguage.googleapis.com/v1/models?key=YOUR_API_KEY"
```

### 方案C: 应用配置修复

#### C1. 修改网络请求超时
```typescript
// 在aiService.ts中增加超时时间
const response = await fetch(url, {
  method: 'POST',
  headers: { /* ... */ },
  body: JSON.stringify(requestBody),
  signal: AbortSignal.timeout(30000) // 30秒超时
})
```

#### C2. 添加重试机制
```typescript
async function fetchWithRetry(url: string, options: any, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url, options)
      if (response.ok) return response
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}
```

## 🚀 立即执行步骤

### 步骤1: 网络环境检查
1. **打开命令行，执行以下命令：**
   ```bash
   ping google.com
   nslookup generativelanguage.googleapis.com
   ```

2. **如果ping失败，说明需要VPN**
   - 连接VPN到美国/日本/新加坡节点
   - 重新执行ping命令验证

### 步骤2: 浏览器测试
1. **打开浏览器，访问：**
   ```
   https://generativelanguage.googleapis.com/v1/models?key=YOUR_API_KEY
   ```
   （将YOUR_API_KEY替换为你的实际密钥）

2. **预期结果：**
   - 成功：显示JSON格式的模型列表
   - 失败：显示错误信息或无法访问

### 步骤3: 应用内测试
1. **确保VPN连接稳定**
2. **在应用设置中重新配置API密钥**
3. **点击"Google诊断"按钮**
4. **查看新的诊断结果**

## 🛠️ 代码级修复

如果网络问题解决后仍有问题，可以尝试以下代码修复：

### 修复1: 增强错误处理
```typescript
// 在 src/services/aiService.ts 中添加
try {
  const response = await fetch(url, options)
  if (!response.ok) {
    const errorText = await response.text()
    console.error('Google API详细错误:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries()),
      body: errorText
    })
    throw new Error(`Google API错误 ${response.status}: ${errorText}`)
  }
} catch (error) {
  if (error instanceof TypeError && error.message.includes('fetch')) {
    throw new Error('网络连接失败，请检查网络设置或使用VPN')
  }
  throw error
}
```

### 修复2: 添加网络状态检测
```typescript
// 检测网络连接状态
async function checkNetworkConnectivity(): Promise<boolean> {
  try {
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      mode: 'no-cors',
      signal: AbortSignal.timeout(5000)
    })
    return true
  } catch {
    return false
  }
}
```

## 📊 成功验证标准

完成修复后，你应该看到：

### 诊断结果应显示：
```
✅ API密钥测试: 通过
✅ 对话测试: 通过
✅ 可用模型: 3个
✅ AI回复: 测试成功
```

### 实际对话测试：
1. 选择Google模型（如Gemini 1.5 Pro）
2. 发送消息："你好"
3. 应该收到正常的AI回复

## 🔍 故障排除检查清单

- [ ] 网络可以访问Google.com
- [ ] VPN连接稳定（如果需要）
- [ ] API密钥格式正确
- [ ] API密钥有效且有权限
- [ ] 基础URL配置正确
- [ ] 防火墙没有阻止连接
- [ ] 应用已重启
- [ ] 浏览器控制台没有CORS错误

## 🆘 应急替代方案

如果Google API仍无法使用，可以：

1. **继续使用DeepSeek**（已经正常工作）
2. **添加其他国内AI服务商**：
   - 阿里云通义千问
   - 百度文心一言
   - 腾讯混元大模型

3. **使用OpenAI API**（如果网络允许）

## 📞 技术支持

如果仍需帮助，请提供：
1. 网络环境信息（地区、ISP、是否使用VPN）
2. 浏览器控制台的完整错误日志
3. API密钥配置截图（隐藏敏感信息）
4. 诊断工具的完整输出

---

**预计解决时间**: 5-15分钟（主要是VPN连接时间）  
**成功率**: 95%（网络连接问题解决后）
