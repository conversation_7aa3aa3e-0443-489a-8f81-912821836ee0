<template>
  <div v-if="provider" class="provider-detail">
    <div class="detail-header">
      <div class="detail-title">
        <span class="detail-icon">{{ provider.icon || '🤖' }}</span>
        <span class="detail-name">配置 {{ provider.displayName }}</span>
      </div>
      <div class="detail-actions">
        <n-button @click="testConnection" :loading="testing" secondary size="small">
          <template #icon>
            <i class="i-carbon-wifi" />
          </template>
          测试连接
        </n-button>
        <n-button
          v-if="provider.id === 'google'"
          @click="runGoogleDiagnostic"
          :loading="diagnosing"
          type="primary"
          size="small"
        >
          <template #icon>
            <i class="i-carbon-debug" />
          </template>
          Google诊断
        </n-button>
        <n-button
          @click="refreshModels"
          :loading="refreshingModels"
          secondary
          size="small"
        >
          <template #icon>
            <i class="i-carbon-refresh" />
          </template>
          刷新模型
        </n-button>
        <n-button @click="resetConfig" quaternary size="small">
          <template #icon>
            <i class="i-carbon-reset" />
          </template>
        </n-button>
      </div>
    </div>

    <div class="detail-content">
      <!-- API密钥配置 -->
      <div class="detail-section">
        <div class="detail-item-inline">
          <h4>API密钥</h4>
          <n-input
            v-model:value="configForm.apiKey"
            type="password"
            placeholder="输入API密钥..."
            show-password-on="click"
          />
        </div>
      </div>

      <!-- 基础URL配置 -->
      <div class="detail-section">
        <div class="detail-item-inline">
          <h4>基础URL</h4>
          <n-input
            v-model:value="configForm.baseUrl"
            placeholder="https://api.example.com/v1"
          />
        </div>
      </div>

      <!-- 模型选择 -->
      <div class="detail-section">
        <div class="model-header">
          <h4>模型选择</h4>
          <span class="section-subtitle">已选择 {{ selectedModels.length }} 个模型</span>
          <div class="model-actions">
            <n-button @click="selectAllModels" size="small" secondary>全选</n-button>
            <n-button @click="clearAllModels" size="small" secondary>清空</n-button>
          </div>
        </div>

        <div class="model-list">
          <div
            v-for="model in provider.models"
            :key="model.id"
            class="model-item"
            :class="{ 'model-item--selected': selectedModels.includes(model.id) }"
          >
            <n-checkbox
              :checked="selectedModels.includes(model.id)"
              @update:checked="(checked) => toggleModel(model.id, checked)"
            />
            
            <div class="model-info">
              <div class="model-line">
                <span class="model-name">{{ model.displayName }}</span>
                <span class="model-context">{{ formatContextLength(model.contextLength) }}</span>
                <span class="model-price" v-if="model.inputPrice">
                  ${{ model.inputPrice }}/1K tokens
                </span>
                <div class="model-features">
                  <span v-if="model.supportStreaming" class="feature-tag">流式</span>
                  <span v-if="model.supportVision" class="feature-tag">视觉</span>
                  <span v-if="model.supportFunction" class="feature-tag">函数</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Google API 诊断结果 -->
      <div v-if="provider.id === 'google' && diagnosticResult" class="detail-section">
        <h4>Google API 诊断结果</h4>
        <div class="diagnostic-results">
          <!-- API密钥测试结果 -->
          <div class="diagnostic-item">
            <div class="diagnostic-header">
              <n-tag
                :type="diagnosticResult.apiKeyTest.success ? 'success' : 'error'"
                size="small"
              >
                API密钥: {{ diagnosticResult.apiKeyTest.success ? '通过' : '失败' }}
              </n-tag>
            </div>

            <div v-if="!diagnosticResult.apiKeyTest.success" class="diagnostic-error">
              <p><strong>错误:</strong> {{ diagnosticResult.apiKeyTest.error }}</p>
              <div v-if="diagnosticResult.apiKeyTest.suggestions?.length" class="diagnostic-suggestions">
                <strong>建议:</strong>
                <ul>
                  <li v-for="suggestion in diagnosticResult.apiKeyTest.suggestions" :key="suggestion">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>

            <div v-if="diagnosticResult.apiKeyTest.success && diagnosticResult.apiKeyTest.details" class="diagnostic-success">
              <p>可用模型: {{ diagnosticResult.apiKeyTest.details.availableModels }}</p>
            </div>
          </div>

          <!-- 对话测试结果 -->
          <div class="diagnostic-item">
            <div class="diagnostic-header">
              <n-tag
                :type="diagnosticResult.chatTest.success ? 'success' : 'error'"
                size="small"
              >
                对话测试: {{ diagnosticResult.chatTest.success ? '通过' : '失败' }}
              </n-tag>
            </div>

            <div v-if="!diagnosticResult.chatTest.success" class="diagnostic-error">
              <p><strong>错误:</strong> {{ diagnosticResult.chatTest.error }}</p>
              <div v-if="diagnosticResult.chatTest.suggestions?.length" class="diagnostic-suggestions">
                <strong>建议:</strong>
                <ul>
                  <li v-for="suggestion in diagnosticResult.chatTest.suggestions" :key="suggestion">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
            </div>

            <div v-if="diagnosticResult.chatTest.success && diagnosticResult.chatTest.details" class="diagnostic-success">
              <p><strong>AI回复:</strong> {{ diagnosticResult.chatTest.details.response }}</p>
            </div>
          </div>

          <!-- 推荐操作 -->
          <div v-if="diagnosticResult.recommendations?.length" class="diagnostic-item">
            <div class="diagnostic-header">
              <strong>推荐操作:</strong>
            </div>
            <ul class="diagnostic-recommendations">
              <li v-for="recommendation in diagnosticResult.recommendations" :key="recommendation">
                {{ recommendation }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useMessage } from 'naive-ui'
import { useSettingsStore } from '@/stores/settings'
import { debugGoogleApi } from '@/utils/googleApiDebug'
import type { AIProvider } from '@/types'

const props = defineProps<{
  provider: AIProvider
}>()

const emit = defineEmits<{
  (e: 'update', providerId: string, updates: Partial<AIProvider>): void
  (e: 'test-connection', providerId: string, success: boolean): void
}>()

const message = useMessage()
const settingsStore = useSettingsStore()

// 响应式数据
const selectedModels = ref<string[]>([])
const testing = ref(false)
const diagnosing = ref(false)
const refreshingModels = ref(false)
const diagnosticResult = ref<any>(null)

// 配置表单
const configForm = ref({
  apiKey: '',
  baseUrl: ''
})

// 方法
const toggleModel = (modelId: string, checked: boolean) => {
  if (checked) {
    selectedModels.value.push(modelId)
  } else {
    const index = selectedModels.value.indexOf(modelId)
    if (index > -1) {
      selectedModels.value.splice(index, 1)
    }
  }
}

const selectAllModels = () => {
  if (props.provider) {
    selectedModels.value = props.provider.models.map(m => m.id)
  }
}

const clearAllModels = () => {
  selectedModels.value = []
}

const formatContextLength = (length: number) => {
  if (length >= 1000000) {
    return `${(length / 1000000).toFixed(1)}M`
  } else if (length >= 1000) {
    return `${(length / 1000).toFixed(0)}K`
  }
  return length.toString()
}

const testConnection = async () => {
  if (!props.provider) return

  testing.value = true
  try {
    // 真正的API连接测试
    const { AIServiceFactory } = await import('@/services/aiService')

    // 创建AI服务实例
    const aiService = AIServiceFactory.createService(props.provider, settingsStore.chat)

    // 测试连接并获取模型列表
    const isAvailable = await aiService.checkAvailability()

    if (isAvailable) {
      // 连接成功，获取模型列表
      const models = await aiService.getModels()

      if (models && models.length > 0) {
        // 转换API返回的模型格式为应用内部格式
        const formattedModels = models.map((model: any) => ({
          id: model.id,
          name: model.id,
          displayName: model.id.replace(/^models\//, ''), // 移除 models/ 前缀
          contextLength: 32768, // 默认值
          inputPrice: 0.001,
          outputPrice: 0.002,
          supportStreaming: true,
          supportVision: model.id.includes('vision') || model.id.includes('pro'),
          supportFunction: true
        }))

        // 更新提供商的模型列表
        settingsStore.updateProvider(props.provider.id, {
          models: formattedModels
        })

        message.success(`连接测试成功！获取到 ${formattedModels.length} 个模型`)
        console.log('✅ 连接测试成功，模型列表已更新:', formattedModels.length, '个模型')
      } else {
        message.success('连接测试成功！但未获取到模型列表')
      }

      emit('test-connection', props.provider.id, true)
    } else {
      throw new Error('API连接失败')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    message.error(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
    emit('test-connection', props.provider.id, false)
  } finally {
    testing.value = false
  }
}

const saveConfig = () => {
  if (!props.provider) return

  const updates = {
    apiKey: configForm.value.apiKey,
    baseUrl: configForm.value.baseUrl,
    selectedModels: selectedModels.value
  }

  settingsStore.updateProvider(props.provider.id, updates)
  emit('update', props.provider.id, updates)

  message.success('配置已保存')
}

const resetConfig = () => {
  if (!props.provider) return

  configForm.value.apiKey = ''
  configForm.value.baseUrl = ''
  selectedModels.value = []
  diagnosticResult.value = null

  message.info('配置已重置')
}

const runGoogleDiagnostic = async () => {
  if (!props.provider || props.provider.id !== 'google') return

  diagnosing.value = true
  diagnosticResult.value = null

  try {
    console.log('🔍 开始Google API诊断...')
    const result = await debugGoogleApi(props.provider, settingsStore.chat)
    diagnosticResult.value = result
    console.log('📊 诊断结果:', result)

    if (result.apiKeyTest.success && result.chatTest.success) {
      message.success('Google API 诊断通过！诊断报告已保存到桌面')
    } else {
      message.warning('Google API 诊断发现问题，诊断报告已保存到桌面，请查看详细信息')
    }
  } catch (error) {
    console.error('诊断过程出错:', error)
    diagnosticResult.value = {
      apiKeyTest: { success: false, error: '诊断过程出错: ' + (error as Error).message },
      chatTest: { success: false, error: '未执行' },
      configuration: {},
      recommendations: ['检查网络连接', '查看浏览器控制台获取详细错误信息']
    }
    message.error('诊断过程出错，错误报告已保存到桌面')
  } finally {
    diagnosing.value = false
  }
}

// 刷新模型列表
const refreshModels = async () => {
  if (!props.provider) {
    message.error('提供商信息不可用')
    return
  }

  refreshingModels.value = true

  try {
    // 动态导入AI服务
    const { AIServiceFactory } = await import('@/services/aiService')

    // 创建AI服务实例
    const aiService = AIServiceFactory.createService(props.provider, settingsStore.chat)

    // 获取模型列表
    const models = await aiService.getModels()

    if (models && models.length > 0) {
      // 转换API返回的模型格式为应用内部格式
      const formattedModels = models.map((model: any) => ({
        id: model.id,
        name: model.id,
        displayName: model.id.replace(/^models\//, ''), // 移除 models/ 前缀
        contextLength: 32768, // 默认值，可以根据模型类型调整
        inputPrice: 0.001, // 默认价格
        outputPrice: 0.002,
        supportStreaming: true,
        supportVision: model.id.includes('vision') || model.id.includes('pro'),
        supportFunction: true
      }))

      // 更新提供商的模型列表
      settingsStore.updateProvider(props.provider.id, {
        models: formattedModels
      })

      message.success(`成功刷新 ${formattedModels.length} 个模型`)
      console.log('✅ 模型列表已更新:', formattedModels.length, '个模型')
    } else {
      message.warning('未获取到任何模型')
    }
  } catch (error) {
    console.error('刷新模型失败:', error)
    message.error(`刷新模型失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    refreshingModels.value = false
  }
}

// 监听provider变化，更新表单
watch(() => props.provider, (newProvider) => {
  if (newProvider) {
    configForm.value.apiKey = newProvider.apiKey || ''
    configForm.value.baseUrl = newProvider.baseUrl || ''
    // 只有当提供商有保存的选中模型配置时才使用，否则默认为空
    selectedModels.value = newProvider.selectedModels || []
  }
}, { immediate: true })

// 监听配置变化，自动保存
watch(configForm, (newConfig) => {
  if (props.provider) {
    const updates = {
      apiKey: newConfig.apiKey,
      baseUrl: newConfig.baseUrl
    }
    settingsStore.updateProvider(props.provider.id, updates)
  }
}, { deep: true })

// 监听选中模型变化，自动保存
watch(selectedModels, (newSelectedModels) => {
  if (props.provider) {
    const updates = {
      selectedModels: newSelectedModels
    }
    settingsStore.updateProvider(props.provider.id, updates)
  }
}, { deep: true })
</script>

<style scoped>
.provider-detail {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 6px 12px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-icon {
  font-size: 16px;
}

.detail-name {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
}

.detail-actions {
  display: flex;
  gap: 4px;
}

.detail-actions .n-button {
  font-size: 11px;
  padding: 2px 6px;
  height: 24px;
}

.detail-content {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 12px;
}

.detail-section h4 {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}



.detail-item {
  margin-bottom: 4px;
}

.detail-item-inline {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 4px;
}

.detail-item-inline h4 {
  margin: 0;
  min-width: 50px;
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.detail-item-inline .n-input {
  flex: 1;
}

.detail-item-inline .n-input :deep(.n-input__input-el) {
  font-size: 12px;
  height: 28px;
  padding: 4px 8px;
}

.detail-item-inline .n-input :deep(.n-input__placeholder) {
  font-size: 12px;
}

.detail-hint {
  display: block;
  margin-top: 4px;
  font-size: 14px;
  color: #9ca3af;
}

.model-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.model-header h4 {
  margin: 0;
  flex-shrink: 0;
}

.section-subtitle {
  font-size: 11px;
  color: #6b7280;
  flex: 1;
}

.model-actions {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.model-actions .n-button {
  font-size: 11px;
  padding: 1px 4px;
  height: 20px;
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  height: 150px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 3px;
  padding: 6px;
  background: #fafafa;
  box-sizing: border-box;
}

.model-item {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  padding: 6px;
  border: 1px solid #e5e7eb;
  border-radius: 3px;
  transition: all 0.2s ease;
  background: white;
}

.model-item:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

.model-item--selected {
  background: #f8fafc;
  border-color: #e5e7eb;
}

.model-info {
  flex: 1;
}

.model-line {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
}

.model-name {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
  flex-shrink: 0;
}

.model-context {
  background: #f3f4f6;
  padding: 1px 4px;
  border-radius: 3px;
  font-size: 10px;
  color: #6b7280;
  flex-shrink: 0;
}

.model-price {
  color: #059669;
  font-size: 10px;
  flex-shrink: 0;
}

.model-features {
  display: flex;
  gap: 3px;
  flex-shrink: 0;
}

.feature-tag {
  background: #dbeafe;
  color: #1e40af;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  line-height: 1.2;
}

/* Google 诊断结果样式 */
.diagnostic-results {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
  padding: 8px;
}

.diagnostic-item {
  margin-bottom: 8px;
  padding: 6px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.diagnostic-item:last-child {
  margin-bottom: 0;
}

.diagnostic-header {
  margin-bottom: 4px;
  font-size: 11px;
  font-weight: 500;
}

.diagnostic-error {
  font-size: 10px;
  color: #dc2626;
}

.diagnostic-error p {
  margin: 2px 0;
}

.diagnostic-suggestions {
  margin-top: 4px;
}

.diagnostic-suggestions ul {
  margin: 2px 0;
  padding-left: 12px;
}

.diagnostic-suggestions li {
  margin: 1px 0;
  font-size: 10px;
}

.diagnostic-success {
  font-size: 10px;
  color: #059669;
}

.diagnostic-success p {
  margin: 2px 0;
}

.diagnostic-recommendations {
  margin: 4px 0;
  padding-left: 12px;
  font-size: 10px;
}

.diagnostic-recommendations li {
  margin: 1px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .detail-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .model-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
